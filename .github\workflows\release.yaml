name: Artifact

on: [push, pull_request]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  artifact:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: actions/cache@v4
        with:
          path: .cache
          key: nix-v1-2.24.9-${{ hashFiles('nix/nixpkgs.json') }}

      - run: sudo apt-get update

      - run: sudo apt-get install -y make git gcc build-essential pkgconf libtool libsystemd-dev libcap-dev libseccomp-dev libyajl-dev go-md2man libtool autoconf python3 automake libprotobuf-c-dev

      - run: |
          set -ex
          sudo mkdir -p .cache
          sudo mv .cache /nix
          if [[ -z $(ls -A /nix) ]]; then sudo docker run --rm --privileged -v /:/mnt nixos/nix:2.24.9 cp -rfT /nix /mnt/nix; fi
          sudo RUNTIME=docker SKIP_CHECKS=1 SKIP_GPG=1 build-aux/release.sh
          sudo mv /nix .cache
          sudo chown -Rf $(whoami) .cache

      - run: |
          sudo mv release-* /tmp/artifact
          for ARCH in amd64 arm64 ppc64le riscv64 s390x; do
            sudo mv /tmp/artifact/crun-*-linux-${ARCH} /tmp/artifact/crun-linux-${ARCH}
            sudo mv /tmp/artifact/crun-*-linux-${ARCH}-disable-systemd /tmp/artifact/crun-linux-${ARCH}-disable-systemd
          done
          sudo mv /tmp/artifact/*.tar.gz /tmp/artifact/crun.tar.gz
          sudo mv /tmp/artifact/*.tar.zst /tmp/artifact/crun.tar.zst
          (cd /tmp/artifact; sha256sum * | sudo tee CHECKSUMS)

      - uses: actions/upload-artifact@v4
        with:
          name: crun-linux-amd64
          path: /tmp/artifact/crun-linux-amd64

      - uses: actions/upload-artifact@v4
        with:
          name: crun-linux-amd64-disable-systemd
          path: /tmp/artifact/crun-linux-amd64-disable-systemd

      - uses: actions/upload-artifact@v4
        with:
          name: crun-linux-arm64
          path: /tmp/artifact/crun-linux-arm64

      - uses: actions/upload-artifact@v4
        with:
          name: crun-linux-arm64-disable-systemd
          path: /tmp/artifact/crun-linux-arm64-disable-systemd

      - uses: actions/upload-artifact@v4
        with:
          name: crun-linux-ppc64le
          path: /tmp/artifact/crun-linux-ppc64le

      - uses: actions/upload-artifact@v4
        with:
          name: crun-linux-ppc64le-disable-systemd
          path: /tmp/artifact/crun-linux-ppc64le-disable-systemd

      - uses: actions/upload-artifact@v4
        with:
          name: crun-linux-riscv64
          path: /tmp/artifact/crun-linux-riscv64

      - uses: actions/upload-artifact@v4
        with:
          name: crun-linux-riscv64-disable-systemd
          path: /tmp/artifact/crun-linux-riscv64-disable-systemd

      - uses: actions/upload-artifact@v4
        with:
          name: crun-linux-s390x
          path: /tmp/artifact/crun-linux-s390x

      - uses: actions/upload-artifact@v4
        with:
          name: crun-linux-s390x-disable-systemd
          path: /tmp/artifact/crun-linux-s390x-disable-systemd

      - uses: actions/upload-artifact@v4
        with:
          name: crun.tar.gz
          path: /tmp/artifact/crun.tar.gz

      - uses: actions/upload-artifact@v4
        with:
          name: crun.tar.zst
          path: /tmp/artifact/crun.tar.zst

      - uses: actions/upload-artifact@v4
        with:
          name: CHECKSUMS
          path: /tmp/artifact/CHECKSUMS
