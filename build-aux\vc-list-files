#!/bin/sh
# List version-controlled file names.

# Print a version string.
scriptversion=2016-01-11.22; # UTC

# Copyright (C) 2006-2017 Free Software Foundation, Inc.

# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.

# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.


# List the specified version-controlled files.
# With no argument, list them all.  With a single DIRECTORY argument,
# list the version-controlled files in that directory.

# If there's an argument, it must be a single, "."-relative directory name.
# cvsu is part of the cvsutils package: http://www.red-bean.com/cvsutils/

postprocess=
case $1 in
  --help) cat <<EOF
Usage: $0 [-C SRCDIR] [DIR...]

Output a list of version-controlled files in DIR (default .), relative to
SRCDIR (default .).  SRCDIR must be the top directory of a checkout.

Options:
  --help     print this help, then exit
  --version  print version number, then exit
  -C SRCDIR  change directory to SRCDIR before generating list

Report bugs and patches to <<EMAIL>>.
EOF
    exit ;;

  --version)
    year=`echo "$scriptversion" | sed 's/[^0-9].*//'`
    cat <<EOF
vc-list-files $scriptversion
Copyright (C) $year Free Software Foundation, Inc,
License GPLv3+: GNU GPL version 3 or later <http://gnu.org/licenses/gpl.html>
This is free software: you are free to change and redistribute it.
There is NO WARRANTY, to the extent permitted by law.
EOF
    exit ;;

  -C)
    test "$2" = . || postprocess="| sed 's|^|$2/|'"
    cd "$2" || exit 1
    shift; shift ;;
esac

test $# = 0 && set .

for dir
do
  if test -d .git; then
    test "x$dir" = x. \
      && dir= sed_esc= \
      || { dir="$dir/"; sed_esc=`echo "$dir"|env sed 's,\([\\/]\),\\\\\1,g'`; }
    # Ignore git symlinks - either they point into the tree, in which case
    # we don't need to visit the target twice, or they point somewhere
    # else (often into a submodule), in which case the content does not
    # belong to this package.
    eval exec git ls-tree -r 'HEAD:"$dir"' \
      \| sed -n '"s/^100[^	]*./$sed_esc/p"' $postprocess
  elif test -d .hg; then
    eval exec hg locate '"$dir/*"' $postprocess
  elif test -d .bzr; then
    test "$postprocess" = '' && postprocess="| sed 's|^\./||'"
    eval exec bzr ls -R --versioned '"$dir"' $postprocess
  elif test -d CVS; then
    test "$postprocess" = '' && postprocess="| sed 's|^\./||'"
    if test -x build-aux/cvsu; then
      eval build-aux/cvsu --find --types=AFGM '"$dir"' $postprocess
    elif (cvsu --help) >/dev/null 2>&1; then
      eval cvsu --find --types=AFGM '"$dir"' $postprocess
    else
      eval awk -F/ \''{			\
          if (!$1 && $3 !~ /^-/) {	\
            f=FILENAME;			\
            if (f ~ /CVS\/Entries$/)	\
              f = substr(f, 1, length(f)-11); \
            print f $2;			\
          }}'\''				\
        `find "$dir" -name Entries -print` /dev/null' $postprocess
    fi
  elif test -d .svn; then
    eval exec svn list -R '"$dir"' $postprocess
  else
    echo "$0: Failed to determine type of version control used in `pwd`" 1>&2
    exit 1
  fi
done

# Local variables:
# eval: (add-hook 'write-file-hooks 'time-stamp)
# time-stamp-start: "scriptversion="
# time-stamp-format: "%:y-%02m-%02d.%02H"
# time-stamp-time-zone: "UTC0"
# time-stamp-end: "; # UTC"
# End:
