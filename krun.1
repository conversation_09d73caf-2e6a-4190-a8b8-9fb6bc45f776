.nh
.TH crun 1 "User Commands"

.SH NAME
krun \- crun based OCI runtime using libkrun to run containerized programs in
isolated KVM environments


.SH SYNOPSIS
krun [global options] command [command options] [arguments...]


.SH DESCRIPTION
krun is a sub package of the crun command line program for running Linux
containers that follow the Open Container Initiative (OCI) format. The krun
command is a symbolic link to the crun executable, that tells crun to run in
krun mode.

.PP
krun uses the dynamic libkrun library to run processes in an isolated
environment using KVM Virtualization.

.PP
libkrun integrates a VMM (Virtual Machine Monitor, the userspace side of a
Hypervisor) with the minimum amount of emulated devices required for its
purpose, abstracting most of the complexity from Virtual Machine management.

.PP
Because of the additional isolation, sharing content with processes and other
containers outside of the krun VM is more difficult.


.SH COMMANDS
See crun.1 man page for the commands available to krun


.SH SEE ALSO
crun.1
