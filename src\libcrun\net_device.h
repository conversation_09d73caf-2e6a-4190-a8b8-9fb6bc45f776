/*
 * crun - OCI runtime written in C
 *
 * Copyright (C) 2025 <PERSON> <g<PERSON><PERSON><PERSON>@scrivano.org>
 * crun is free software; you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License as published by
 * the Free Software Foundation; either version 2.1 of the License, or
 * (at your option) any later version.
 *
 * crun is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with crun.  If not, see <http://www.gnu.org/licenses/>.
 */

#ifndef NET_DEVICE_H
#define NET_DEVICE_H

#include <config.h>
#include <ocispec/runtime_spec_schema_config_schema.h>
#include "error.h"

int move_network_device (const char *ifname, const char *newifname, int netns_fd, libcrun_error_t *err);

#endif
