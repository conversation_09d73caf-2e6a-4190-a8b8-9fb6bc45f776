'\" t
.nh
.TH crun 1 "User Commands"

.SH NAME
crun \- a fast and lightweight OCI runtime


.SH SYNOPSIS
crun [global options] command [command options] [arguments...]


.SH DESCRIPTION
crun is a command line program for running Linux containers that
follow the Open Container Initiative (OCI) format.


.SH COMMANDS
\fBcreate\fP
Create a container.  The runtime detaches from the container process
once the container environment is created.  It is necessary to
successively use \fBstart\fR for starting the container.

.PP
\fBdelete\fP
Remove definition for a container.

.PP
\fBexec\fP
Exec a command in a running container.

.PP
\fBlist\fP
List known containers.

.PP
\fBmounts add\fP
Add mounts while the container is running.  It requires two arguments: the container ID and a JSON file containing the mounts section of the OCI config file.  Each mount listed there is added to the running container.  The command is experimental and can be changed without notice.

.PP
\fBmounts remove\fP
Remove mounts while the container is running.  It requires two arguments: the container ID and a JSON file containing the mounts section of the OCI config file.  Only the destination attribute for each mount is used.  The command is experimental and can be changed without notice.

.PP
\fBkill\fP
Send the specified signal to the container init process.  If no signal
is specified, SIGTERM is used.

.PP
\fBps\fP
Show the processes running in a container.

.PP
\fBrun\fP
Create and immediately start a container.

.PP
\fBspec\fP
Generate a configuration file.

.PP
\fBstart\fP
Start a container that was previously created.  A container cannot be
started multiple times.

.PP
\fBstate\fP
Output the state of a container.

.PP
\fBpause\fP
Pause all the processes in the container.

.PP
\fBresume\fP
Resume the processes in the container.

.PP
\fBupdate\fP
Update container resource constraints.

.PP
\fBcheckpoint\fP
Checkpoint a running container using CRIU.

.PP
\fBrestore\fP
Restore a container from a checkpoint.


.SH STATE
By default, when running as root user, crun saves its state under the
\fB/run/crun\fP directory.  As unprivileged user, instead the
\fIXDG_RUNTIME_DIR\fP environment variable is honored, and the directory
\fB$XDG_RUNTIME_DIR/crun\fP is used.  The global option \fB--root\fP
overrides this setting.


.SH GLOBAL OPTIONS
\fB--debug\fP
Produce verbose output.

.PP
\fB--log\fP=\fILOG-DESTINATION\fP
Define the destination for the error and warning messages generated by
crun.
If the error happens late in the container init process, when crun
already stopped watching it, then it will be printed to the container
stderr.

.PP
It is specified in the form \fIBACKEND:SPECIFIER\fP\&.

.PP
These following backends are supported:
.IP \(bu 2
file:PATH
.IP \(bu 2
journald:IDENTIFIER
.IP \(bu 2
syslog:IDENTIFIER

.PP
If no backend is specified, then \fIfile:\fP is used by default.

.PP
\fB--log-format\fP=\fIFORMAT\fP
Define the format of the log messages.  It can either be \fBtext\fP, or
\fBjson\fP\&.  The default is \fBtext\fP\&.

.PP
\fB--log-level\fP=\fILEVEL\fP
Define the log level.  It can either be \fBdebug\fP, \fBwarning\fP or \fBerror\fP\&.
The default is \fBerror\fP\&.

.PP
\fB--no-pivot\fP
Use \fBchroot(2)\fR instead of \fBpivot_root(2)\fR when creating the container.
This option is not safe, and should be avoided.

.PP
\fB--root\fP=\fIDIR\fP
Defines where to store the state for crun containers.

.PP
\fB--systemd-cgroup\fP
Use systemd for configuring cgroups.  If not specified, the cgroup is
created directly using the cgroupfs backend.

.PP
\fB--cgroup-manager\fP=\fIMANAGER\fP
Specify what cgroup manager must be used.  Permitted values are \fBcgroupfs\fP,
\fBsystemd\fP and \fBdisabled\fP\&.

.PP
\fB-?\fP, \fB--help\fP
Print a help list.

.PP
\fB--usage\fP
Print a short usage message.

.PP
\fB-V\fP, \fB--version\fP
Print program version

.SH CREATE OPTIONS
crun [global options] create [options] CONTAINER

.PP
\fB--bundle\fP=\fIPATH\fP
Path to the OCI bundle, by default it is the current directory.

.PP
\fB--config\fP=\fIFILE\fP
Override the configuration file to use.  The default value is \fBconfig.json\fP\&.

.PP
\fB--console-socket\fP=\fISOCKET\fP
Path to a UNIX socket that will receive the ptmx end of the tty for
the container.

.PP
\fB--no-new-keyring\fP
Keep the same session key

.PP
\fB--preserve-fds\fP=\fIN\fP
Additional number of FDs to pass into the container.

.PP
\fB--pid-file\fP=\fIPATH\fP
Path to the file that will contain the container process PID.

.SH RUN OPTIONS
crun [global options] run [options] CONTAINER

.PP
\fB--bundle\fP=\fIBUNDLE\fP
Path to the OCI bundle, by default it is the current directory.

.PP
\fB--config\fP=\fIFILE\fP
Override the configuration file to use.  The default value is \fBconfig.json\fP\&.

.PP
\fB--console-socket\fP=\fISOCKET\fP
Path to a UNIX socket that will receive the ptmx end of the tty for
the container.

.PP
\fB--no-new-keyring\fP
Keep the same session key.

.PP
\fB--preserve-fds\fP=\fIN\fP
Additional number of FDs to pass into the container.

.PP
\fB--pid-file\fP=\fIPATH\fP
Path to the file that will contain the container process PID.

.PP
\fB--detach\fP
Detach the container process from the current session.

.SH DELETE OPTIONS
crun [global options] delete [options] CONTAINER

.PP
\fB--force\fP
Delete the container even if it is still running.

.PP
\fB--regex\fP=\fIREGEX\fP
Delete all the containers that satisfy the specified regex.

.SH EXEC OPTIONS
crun [global options] exec [options] CONTAINER CMD

.PP
\fB--apparmor\fP=\fIPROFILE\fP
Set the apparmor profile for the process.

.PP
\fB--console-socket\fP=\fISOCKET\fP
Path to a UNIX socket that will receive the ptmx end of the tty for
the container.

.PP
\fB--cwd\fP=\fIPATH\fP
Set the working directory for the process to PATH.

.PP
\fB--cap\fP=\fICAP\fP
Specify an additional capability to add to the process.

.PP
\fB--detach\fP
Detach the container process from the current session.

.PP
\fB--cgroup\fP=\fIPATH\fP
Specify a sub-cgroup path inside the container cgroup.  The path must
already exist in the container cgroup.

.PP
\fB--env\fP=\fIENV\fP
Specify an environment variable.

.PP
\fB--no-new-privs\fP
Set the no new privileges value for the process.

.PP
\fB--preserve-fds\fP=\fIN\fP
Additional number of FDs to pass into the container.

.PP
\fB--process\fP=\fIFILE\fP
Path to a file containing the process JSON configuration.

.PP
\fB--process-label\fP=\fIVALUE\fP
Set the asm process label for the process commonly used with selinux.

.PP
\fB--pid-file\fP=\fIPATH\fP
Path to the file that will contain the new process PID.

.PP
\fB-t\fP \fB--tty\fP
Allocate a pseudo TTY.

.PP
**-u \fIUSERSPEC\fP \fB--user\fP=\fIUSERSPEC\fP
Specify the user in the form UID[:GID].

.SH LIST OPTIONS
crun [global options] list [options]

.PP
\fB-q\fP \fB--quiet\fP
Show only the container ID.

.SH KILL OPTIONS
crun [global options] kill [options] CONTAINER SIGNAL

.PP
\fB--all\fP
Kill all the processes in the container.

.PP
\fB--regex\fP=\fIREGEX\fP
Kill all the containers that satisfy the specified regex.

.SH PS OPTIONS
crun [global options] ps [options]

.PP
\fB--format\fP=\fIFORMAT\fP
Specify the output format.  It must be either \fBtable\fR or \fBjson\fR\&.
By default \fBtable\fR is used.

.SH SPEC OPTIONS
crun [global options] spec [options]

.PP
\fB-b\fP \fIDIR\fP \fB--bundle\fP=\fIDIR\fP
Path to the root of the bundle dir (default ".").

.PP
\fB--rootless\fP
Generate a config.json file that is usable by an unprivileged user.

.SH UPDATE OPTIONS
crun [global options] update [options] CONTAINER

.PP
\fB--blkio-weight\fP=\fIVALUE\fP
Specifies per cgroup weight.

.PP
\fB--cpu-period\fP=\fIVALUE\fP
CPU CFS period to be used for hardcapping.

.PP
\fB--cpu-quota\fP=\fIVALUE\fP
CPU CFS hardcap limit.

.PP
\fB--cpu-rt-period\fP=\fIVALUE\fP
CPU realtime period to be used for hardcapping.

.PP
\fB--cpu-rt-runtime\fP=\fIVALUE\fP
CPU realtime hardcap limit.

.PP
\fB--cpu-share\fP=\fIVALUE\fP
CPU shares.

.PP
\fB--cpuset-cpus\fP=\fIVALUE\fP
CPU(s) to use.

.PP
\fB--cpuset-mems\fP=\fIVALUE\fP
Memory node(s) to use.

.PP
\fB--kernel-memory\fP=\fIVALUE\fP
Kernel memory limit.

.PP
\fB--kernel-memory-tcp\fP=\fIVALUE\fP
Kernel memory limit for TCP buffer.

.PP
\fB--memory\fP=\fIVALUE\fP
Memory limit.

.PP
\fB--memory-reservation\fP=\fIVALUE\fP
Memory reservation or soft_limit.

.PP
\fB--memory-swap\fP=\fIVALUE\fP
Total memory usage.

.PP
\fB--pids-limit\fP=\fIVALUE\fP
Maximum number of pids allowed in the container.

.PP
\fB-r\fP, \fB--resources\fP=\fIFILE\fP
Path to the file containing the resources to update.

.SH CHECKPOINT OPTIONS
crun [global options] checkpoint [options] CONTAINER

.PP
\fB--image-path\fP=\fIDIR\fP
Path for saving CRIU image files

.PP
\fB--work-path\fP=\fIDIR\fP
Path for saving work files and logs

.PP
\fB--leave-running\fP
Leave the process running after checkpointing

.PP
\fB--tcp-established\fP
Allow open TCP connections

.PP
\fB--ext-unix-sk\fP
Allow external UNIX sockets

.PP
\fB--shell-job\fP
Allow shell jobs

.PP
\fB--pre-dump\fP
Only checkpoint the container's memory without stopping the container.
It is not possible to restore a container from a pre-dump. A pre-dump
always needs a final checkpoint (without \fB--pre-dump\fP). It is possible
to make as many pre-dumps as necessary. For a second pre-dump or for
a final checkpoint it is necessary to use \fB--parent-path\fP to point
crun (and thus CRIU) to the pre-dump.

.PP
\fB--parent-path\fP=\fIDIR\fP
Doing multiple pre-dumps or the final checkpoint after one or multiple
pre-dumps requires that crun (and thus CRIU) knows the location of
the pre-dump. It is important to use a relative path from the actual
checkpoint directory specified via \fB--image-path\fP\&. It will fail
if an absolute path is used.

.PP
\fB--manage-cgroups-mode\fP=\fIMODE\fP
Specify which CRIU manage cgroup mode should be used. Permitted values are
\fBsoft\fP, \fBignore\fP, \fBfull\fP or \fBstrict\fP\&. Default is \fBsoft\fP\&.

.SH RESTORE OPTIONS
crun [global options] restore [options] CONTAINER

.PP
\fB-b DIR\fP \fB--bundle\fP=\fIDIR\fP
Container bundle directory (default ".")

.PP
\fB--image-path\fP=\fIDIR\fP
Path for saving CRIU image files

.PP
\fB--work-path\fP=\fIDIR\fP
Path for saving work files and logs

.PP
\fB--tcp-established\fP
Allow open TCP connections

.PP
\fB--ext-unix\fP
Allow external UNIX sockets

.PP
\fB--shell-job\fP
Allow shell jobs

.PP
\fB--detach\fP
Detach from the container's process

.PP
\fB--pid-file\fP=\fIFILE\fP
Where to write the PID of the container

.PP
\fB--manage-cgroups-mode\fP=\fIMODE\fP
Specify which CRIU manage cgroup mode should be used. Permitted values are
\fBsoft\fP, \fBignore\fP, \fBfull\fP or \fBstrict\fP\&. Default is \fBsoft\fP\&.

.PP
\fB--lsm-profile\fP=\fITYPE\fP:\fINAME\fP
Specify an LSM profile to be used during restore.
\fITYPE\fP can be either \fBapparmor\fP or \fBselinux\fP\&.

.PP
\fB--lsm-mount-context\fP=\fIVALUE\fP
Specify a new LSM mount context to be used during restore.
This option replaces an existing mount context information
with the specified value. This is useful when restoring
a container into an existing Pod and selinux labels
need to be changed during restore.


.SH Extensions to OCI
.SH \fBrun.oci.mount_context_type=context\fR
Set the mount context type on volumes mounted with SELinux labels.

.PP
Valid context types are:
  context (default)
  fscontext
  defcontext
  rootcontext

.PP
More information on how the context mount flags works see the \fBmount(8)\fR man page.

.SH \fBrun.oci.seccomp.receiver=PATH\fR
If the annotation \fBrun.oci.seccomp.receiver=PATH\fR is specified, the
seccomp listener is sent to the UNIX socket listening on the specified
path.  It can also set with the \fBRUN_OCI_SECCOMP_RECEIVER\fR environment variable.
It is an experimental feature, and the annotation will be removed once
it is supported in the OCI runtime specs.  It must be an absolute path.

.SH \fBrun.oci.seccomp.plugins=PATH\fR
If the annotation \fBrun.oci.seccomp.plugins=PLUGIN1[:PLUGIN2]...\fR is specified, the
seccomp listener fd is handled through the specified plugins.  The
plugin must either be an absolute path or a file name that is looked
up by \fBdlopen(3)\fR\&.  More information on how the lookup is performed
are available on the \fBld.so(8)\fR man page.

.SH \fBrun.oci.seccomp_fail_unknown_syscall=1\fR
If the annotation \fBrun.oci.seccomp_fail_unknown_syscall\fR is present, then crun
will fail when an unknown syscall is encountered in the seccomp configuration.

.SH \fBrun.oci.seccomp_bpf_data=PATH\fR
If the annotation \fBrun.oci.seccomp_bpf_data\fR is present, then crun
ignores the seccomp section in the OCI configuration file and use the specified data
as the raw data to the \fBseccomp(SECCOMP_SET_MODE_FILTER)\fR syscall.
The data must be encoded in base64.

.PP
It is an experimental feature, and the annotation will be removed once
it is supported in the OCI runtime specs.

.SH \fBrun.oci.keep_original_groups=1\fR
If the annotation \fBrun.oci.keep_original_groups\fR is present, then crun
will skip the \fBsetgroups\fR syscall that is used to either set the
additional groups specified in the OCI configuration, or to reset the
list of additional groups if none is specified.

.SH \fBrun.oci.pidfd_receiver=PATH\fR
It is an experimental feature and will be removed once the feature is in the
OCI runtime specs.

.PP
If present, specify the path to the UNIX socket that will receive the
pidfd for the container process.

.SH \fBrun.oci.systemd.force_cgroup_v1=/PATH\fR
If the annotation \fBrun.oci.systemd.force_cgroup_v1=/PATH\fR is present, then crun
will override the specified mount point \fB/PATH\fR with a cgroup v1 mount
made of a single hierarchy \fBnone,name=systemd\fR\&.
It is useful to run on a cgroup v2 system containers using older
versions of systemd that lack support for cgroup v2.

.PP
\fBNote\fP: Your container host has to have the cgroup v1 mount already present, otherwise
this will not work. If you want to run the container rootless, the user it runs under
has to have permissions to this mountpoint.

.PP
For example, as root:

.EX
mkdir /sys/fs/cgroup/systemd
mount cgroup -t cgroup /sys/fs/cgroup/systemd -o none,name=systemd,xattr
chown -R the_user.the_user /sys/fs/cgroup/systemd
.EE

.SH \fBrun.oci.systemd.subgroup=SUBGROUP\fR
Override the name for the systemd sub cgroup created under the systemd
scope, so the final cgroup will be like:

.EX
/sys/fs/cgroup/$PATH/$SUBGROUP
.EE

.PP
When it is set to the empty string, a sub cgroup is not created.

.PP
If not specified, it defaults to \fBcontainer\fR on cgroup v2, and to \fB""\fR
on cgroup v1.

.PP
e.g.

.EX
/sys/fs/cgroup//system.slice/foo-352700.scope/container
.EE

.SH \fBrun.oci.delegate-cgroup=DELEGATED-CGROUP\fR
If the \fBrun.oci.systemd.subgroup\fR annotation is specified, yet another
sub-cgroup is created and the container process is moved here.

.PP
If a cgroup namespace is used, the cgroup namespace is created before
moving the container to the delegated cgroup.

.EX
/sys/fs/cgroup/$PATH/$SUBGROUP/$DELEGATED-CGROUP
.EE

.PP
The runtime doesn't apply any limit to the \fB$DELEGATED-CGROUP\fR
sub-cgroup, the runtime uses only \fB$PATH/$SUBGROUP\fR\&.

.PP
The container payload fully manages \fB$DELEGATE-CGROUP\fR, the limits
applied to \fB$PATH/$SUBGROUP\fR still applies to \fB$DELEGATE-CGROUP\fR\&.

.PP
Since cgroup delegation is not safe on cgroup v1, this option is
supported only on cgroup v2.

.SH \fBrun.oci.hooks.stdout=FILE\fR
If the annotation \fBrun.oci.hooks.stdout\fR is present, then crun
will open the specified file and use it as the stdout for the hook
processes.  The file is opened in append mode and it is created if it
doesn't already exist.

.SH \fBrun.oci.hooks.stderr=FILE\fR
If the annotation \fBrun.oci.hooks.stderr\fR is present, then crun
will open the specified file and use it as the stderr for the hook
processes.  The file is opened in append mode and it is created if it
doesn't already exist.

.SH \fBrun.oci.handler=HANDLER\fR
It is an experimental feature.

.PP
If specified, run the specified handler for execing the container.
The only supported values are \fBkrun\fR and \fBwasm\fR\&.
.IP \(bu 2
\fBkrun\fR: When \fBkrun\fR is specified, the \fBlibkrun.so\fR shared object is loaded
and it is used to launch the container using libkrun.
.IP \(bu 2
\fBwasm\fR: If specified, run the wasm handler for container. Allows running wasm
workload natively. Accepts a \fB\&.wasm\fR binary as input and if \fB\&.wat\fR is
provided it will be automatically compiled into a wasm module. Stdout of
wasm module is relayed back via crun.

.SH tmpcopyup mount options
If the \fBtmpcopyup\fR option is specified for a tmpfs, then the path that
is shadowed by the tmpfs mount is recursively copied up to the tmpfs
itself.

.SH copy-symlink mount options
If the \fBcopy-symlink\fR option is specified, if the source of a bind
mount is a symlink, the symlink is recreated at the specified
destination instead of attempting a mount that would resolve the
symlink itself.  If the destination already exists and it is not a
symlink with the expected content, crun will return an error.

.SH dest-nofollow
When this option is specified for a bind mount, and the destination of
the bind mount is a symbolic link, \fBcrun\fR will mount the symbolic link
itself at the target destination.

.SH src-nofollow
When this option is specified for a bind mount, and the source of the
bind mount is a symbolic link, \fBcrun\fR will use the symlink itself
rather than the file or directory the symbolic link points to.

.SH r$FLAG mount options
If a \fBr$FLAG\fR mount option is specified then the flag \fB$FLAG\fR is set
recursively for each children mount.

.PP
These flags are supported:
.IP \(bu 2
"rro"
.IP \(bu 2
"rrw"
.IP \(bu 2
"rsuid"
.IP \(bu 2
"rnosuid"
.IP \(bu 2
"rdev"
.IP \(bu 2
"rnodev"
.IP \(bu 2
"rexec"
.IP \(bu 2
"rnoexec"
.IP \(bu 2
"rsync"
.IP \(bu 2
"rasync"
.IP \(bu 2
"rdirsync"
.IP \(bu 2
"rmand"
.IP \(bu 2
"rnomand"
.IP \(bu 2
"ratime"
.IP \(bu 2
"rnoatime"
.IP \(bu 2
"rdiratime"
.IP \(bu 2
"rnodiratime"
.IP \(bu 2
"rrelatime"
.IP \(bu 2
"rnorelatime"
.IP \(bu 2
"rstrictatime"
.IP \(bu 2
"rnostrictatime"

.SH idmap mount options
If the \fBidmap\fR option is specified then the mount is ID mapped using
the container target user namespace.  This is an experimental feature
and can change at any time without notice.

.PP
The \fBidmap\fR option supports a custom mapping that can be different
than the user namespace used by the container.

.PP
The mapping can be specified after the \fBidmap\fR option like:
\fBidmap=uids=0-1-10#10-11-10;gids=0-100-10\fR\&.

.PP
For each triplet, the first value is the start of the backing
file system IDs that are mapped to the second value on the host.  The
length of this mapping is given in the third value.

.PP
Multiple ranges are separated with \fB#\fR\&.

.PP
These values are written to the \fB/proc/$PID/uid_map\fR and
\fB/proc/$PID/gid_map\fR files to create the user namespace for the
idmapped mount.

.PP
The only two options that are currently supported after \fBidmap\fR are
\fBuids\fR and \fBgids\fR\&.

.PP
When a custom mapping is specified, a new user namespace is created
for the idmapped mount.

.PP
If no option is specified, then the container user namespace is used.

.PP
If the specified mapping is prepended with a '@' then the mapping is
considered relative to the container user namespace.  The host ID for
the mapping is changed to account for the relative position of the
container user in the container user namespace.

.PP
For example, the mapping: \fBuids=@1-3-10\fR, given a configuration like

.EX
"uidMappings": [
      {
        "containerID": 0,
        "hostID": 0,
        "size": 1
      },
      {
        "containerID": 1,
        "hostID": 2,
        "size": 1000
      }
    ]
.EE

.PP
will be converted to the absolute value \fBuids=1-4-10\fR, where 4 is
calculated by adding 3 (container ID in the \fBuids=\fR mapping) and 1
(\fBhostID - containerID\fR for the user namespace mapping where
\fBcontainerID = 1\fR is found).

.PP
The current implementation doesn't take into account multiple
user namespace ranges, so it is the caller's responsibility to split a
mapping if it overlaps multiple ranges in the user namespace.  In such
a case, there won't be any error reported.

.SH Automatically create user namespace
When running as user different than root, an user namespace is
automatically created even if it is not specified in the config file.
The current user is mapped to the ID 0 in the container, and any
additional id specified in the files \fB/etc/subuid\fR and \fB/etc/subgid\fR
is automatically added starting with ID 1.


.SH CGROUP v1
Support for cgroup v1 is deprecated and will be removed in a future release.


.SH CGROUP v2
\fBNote\fP: cgroup v2 does not yet support control of realtime processes and
the cpu controller can only be enabled when all RT processes are in the root
cgroup. This will make crun fail while running alongside RT processes.

.PP
If the cgroup configuration found is for cgroup v1, crun attempts a
conversion when running on a cgroup v2 system.

.PP
These are the OCI resources currently supported with cgroup v2 and how
they are converted when needed from the cgroup v1 configuration.

.SH Memory controller
.TS
allbox;
l l l l 
l l l l .
\fBOCI (x)\fP	\fBcgroup 2 value (y)\fP	\fBconversion\fP	\fBcomment\fP
limit	memory.max	y = x	
swap	memory.swap.max	y = x - memory_limit	T{
the swap limit on cgroup v1 includes the memory usage too
T}
reservation	memory.low	y = x	
.TE

.SH PIDs controller
.TS
allbox;
l l l l 
l l l l .
\fBOCI (x)\fP	\fBcgroup 2 value (y)\fP	\fBconversion\fP	\fBcomment\fP
limit	pids.max	y = x	
.TE

.SH CPU controller
.TS
allbox;
l l l l 
l l l l .
\fBOCI (x)\fP	\fBcgroup 2 value (y)\fP	\fBconversion\fP	\fBcomment\fP
shares	cpu.weight	T{
y=10^((log2(x)^2 + 125 * log2(x)) / 612.0 - 7.0 / 34.0)
T}
	T{
convert from [2-262144] to [1-10000]
T}
period	cpu.max	y = x	T{
period and quota are written together
T}
quota	cpu.max	y = x	T{
period and quota are written together
T}
.TE

.SH blkio controller
.TS
allbox;
l l l l 
l l l l .
\fBOCI (x)\fP	\fBcgroup 2 value (y)\fP	\fBconversion\fP	\fBcomment\fP
weight	io.bfq.weight	y = x	
weight_device	io.bfq.weight	y = x	
weight	io.weight (fallback)	y = 1 + (x-10)*9999/990	T{
convert linearly from [10-1000] to [1-10000]
T}
weight_device	io.weight (fallback)	y = 1 + (x-10)*9999/990	T{
convert linearly from [10-1000] to [1-10000]
T}
rbps	io.max	y=x	
wbps	io.max	y=x	
riops	io.max	y=x	
wiops	io.max	y=x	
.TE

.SH cpuset controller
.TS
allbox;
l l l l 
l l l l .
\fBOCI (x)\fP	\fBcgroup 2 value (y)\fP	\fBconversion\fP	\fBcomment\fP
cpus	cpuset.cpus	y = x	
mems	cpuset.mems	y = x	
.TE

.SH hugetlb controller
.TS
allbox;
l l l l 
l l l l .
\fBOCI (x)\fP	\fBcgroup 2 value (y)\fP	\fBconversion\fP	\fBcomment\fP
\&.limit_in_bytes	hugetlb.\&.max	y = x	
.TE
