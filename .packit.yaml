---
# See the documentation for more information:
# https://packit.dev/docs/configuration/

downstream_package_name: crun

# Ref: https://packit.dev/docs/configuration#files_to_sync
files_to_sync:
  - src: rpm/gating.yaml
    dest: gating.yaml
  - src: plans/
    dest: plans/
    delete: true
    mkpath: true
  - src: tests/tmt/
    dest: tests/tmt/
    delete: true
    mkpath: true
  - src: .fmf/
    dest: .fmf/
    delete: true
    mkpath: true
  - .packit.yaml

packages:
  crun-fedora:
    pkg_tool: fedpkg
    specfile_path: rpm/crun.spec
  crun-centos:
    pkg_tool: centpkg
    specfile_path: rpm/crun.spec
  crun-eln:
    specfile_path: rpm/crun.spec

srpm_build_deps:
  - git-archive-all
  - make

actions:
  # This action runs only on copr build jobs
  create-archive:
    - "git-archive-all -v --force-submodules rpm/crun-HEAD.tar.xz"
    - bash -c "ls -1 rpm/crun-HEAD.tar.xz"

jobs:
  - job: copr_build
    trigger: pull_request
    packages: [crun-fedora]
    notifications: &copr_build_failure_notification
      failure_comment:
        message: "Ephemeral COPR build failed. @containers/packit-build please check."
    targets: &fedora_copr_targets
      - fedora-all-x86_64
      - fedora-all-aarch64

  - job: copr_build
    trigger: pull_request
    packages: [crun-eln]
    notifications: *copr_build_failure_notification
    targets:
      - fedora-eln-x86_64
      - fedora-eln-aarch64

  - job: copr_build
    trigger: pull_request
    packages: [crun-centos]
    notifications: *copr_build_failure_notification
    targets: &centos_copr_targets
      # Need epel9 repos to fetch wasmedge build dependency
      centos-stream-9-x86_64:
        additional_repos:
          - https://dl.fedoraproject.org/pub/epel/9/Everything/x86_64/
      centos-stream-9-aarch64:
        additional_repos:
          - https://dl.fedoraproject.org/pub/epel/9/Everything/aarch64/
      # TODO: build on CS10 with wasmedge once epel-10 is available
      centos-stream-10-x86_64: {}
      centos-stream-10-aarch64: {}

  # Run on commit to main branch
  - job: copr_build
    trigger: commit
    packages: [crun-fedora]
    notifications:
      failure_comment:
        message: "podman-next COPR build failed. @containers/packit-build please check."
    branch: main
    owner: rhcontainerbot
    project: podman-next

  # Podman system tests for Fedora and CentOS Stream
  - job: tests
    trigger: pull_request
    packages: [crun-fedora]
    notifications: &test_failure_notification
      failure_comment:
        message: "TMT tests failed. @containers/packit-build please check."
    targets: *fedora_copr_targets
    tf_extra_params:
      environments:
        - artifacts:
          - type: repository-file
            id: https://copr.fedorainfracloud.org/coprs/rhcontainerbot/podman-next/repo/fedora-$releasever/rhcontainerbot-podman-next-fedora-$releasever.repo

  # Podman system tests for CentOS Stream
  - job: tests
    trigger: pull_request
    packages: [crun-centos]
    notifications: *test_failure_notification
    # TODO: Re-enable centos-stream-10-x86_64 once criu issues are solved
    # Ref: https://github.com/containers/crun/pull/1758#issuecomment-2901772392
    # Issue filed: https://github.com/containers/crun/issues/1759
    #targets: *centos_copr_targets
    targets:
      - centos-stream-9-x86_64
      - centos-stream-9-aarch64
      - centos-stream-10-aarch64
    tf_extra_params:
      environments:
        - artifacts:
          - type: repository-file
            id: https://copr.fedorainfracloud.org/coprs/rhcontainerbot/podman-next/repo/centos-stream-$releasever/rhcontainerbot-podman-next-centos-stream-$releasever.repo

  - job: propose_downstream
    trigger: release
    packages: [crun-fedora]
    dist_git_branches: &fedora_targets
      - fedora-all

  - job: propose_downstream
    trigger: release
    packages: [crun-centos]
    dist_git_branches:
      - c10s

  - job: koji_build
    trigger: commit
    packages: [crun-fedora]
    dist_git_branches: *fedora_targets

  - job: bodhi_update
    trigger: commit
    packages: [crun-fedora]
    dist_git_branches:
      - fedora-branched # rawhide updates are created automatically
