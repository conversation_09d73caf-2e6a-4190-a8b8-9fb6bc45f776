# crun Rootless模式中用户ID映射机制分析

## 概述

crun的rootless模式允许非特权用户运行容器，其核心机制是通过Linux用户命名空间(user namespace)将host的非root用户映射为容器中的相同或不同的UID。本文档详细分析这一映射过程的实现。

## 关键数据结构

### 容器结构体
```c
struct libcrun_container_s {
    runtime_spec_schema_config_schema *container_def;
    uid_t host_uid;        // host上的用户ID
    gid_t host_gid;        // host上的组ID
    uid_t container_uid;   // 容器内的用户ID
    gid_t container_gid;   // 容器内的组ID
    // ...
};
```

## Rootless模式检测

### 1. 检测函数
```c
static inline int is_rootless(libcrun_error_t *err) {
    if (geteuid())
        return 1;
    return check_running_in_user_namespace(err);
}
```

### 2. 用户命名空间检测
```c
int check_running_in_user_namespace(libcrun_error_t *err) {
    // 读取 /proc/self/uid_map
    ret = read_all_file("/proc/self/uid_map", &buffer, &len, err);
    // 检查是否包含 "4294967295" (表示不在用户命名空间中)
    ret = strstr(buffer, "4294967295") ? 0 : 1;
    return ret;
}
```

## 用户ID映射过程

### 1. 容器初始化时的UID/GID设置
在 `make_container()` 函数中：
```c
container->host_uid = geteuid();  // 获取当前有效用户ID
container->host_gid = getegid();  // 获取当前有效组ID
```

### 2. 强制用户命名空间创建
对于非root用户，crun强制创建用户命名空间：
```c
if (container->host_uid && (ns->all_namespaces & CLONE_NEWUSER) == 0) {
    libcrun_warning("non root user need to have an 'user' namespace");
    ns->all_namespaces |= CLONE_NEWUSER;
    ns->namespaces_to_unshare |= CLONE_NEWUSER;
}
```

### 3. 用户命名空间映射设置

#### 默认映射策略
在 `libcrun_set_usernamespace()` 函数中：

1. **如果配置中没有明确的UID映射**：
   ```c
   if (!def->linux->uid_mappings_len) {
       ret = format_default_id_mapping(&uid_map, container->container_uid, 
                                     container->host_uid, container->host_uid, 1, err);
   }
   ```

2. **默认映射格式化**：
   ```c
   int format_default_id_mapping(char **out, uid_t container_id, uid_t host_uid, 
                                uid_t host_id, int is_uid, libcrun_error_t *err) {
       // 从 /etc/subuid 或 /etc/subgid 获取子ID范围
       if (getsubidrange(host_uid, is_uid, &from, &available) < 0)
           return 0;
       
       // 创建映射：Host ID -> Container ID
       ret = snprintf(buffer + written, remaining, "%d %d 1\n", container_id, host_id);
   }
   ```

### 4. 子ID范围获取
```c
static int getsubidrange(uid_t id, int is_uid, uint32_t *from, uint32_t *len) {
    // 打开 /etc/subuid 或 /etc/subgid
    input = fopen(is_uid ? "/etc/subuid" : "/etc/subgid", "re");
    
    // 查找用户对应的子ID范围
    // 格式: username:start:count
    *from = strtoull(&lineptr[len_name + 1], &endptr, 10);
    *len = strtoull(&endptr[1], &endptr, 10);
}
```

## 映射应用过程

### 1. 使用newuidmap/newgidmap工具
```c
static int newuidmap(pid_t pid, const char *map_file, libcrun_error_t *err) {
    return uidgidmap_helper("newuidmap", pid, map_file, err);
}

static int newgidmap(pid_t pid, const char *map_file, libcrun_error_t *err) {
    return uidgidmap_helper("newgidmap", pid, map_file, err);
}
```

### 2. 直接写入proc文件系统
如果newuidmap/newgidmap失败，直接写入：
```c
// 写入 /proc/[pid]/uid_map
xasprintf(&uid_map_file, "/proc/%d/uid_map", pid);
ret = write_file(uid_map_file, uid_map, uid_map_len, err);

// 对于单一映射的特殊处理
if (is_single_mapping(def->linux->uid_mappings, def->linux->uid_mappings_len, 
                     container->host_uid, container->container_uid)) {
    ret = format_mount_mapping(&single_mapping, container->container_uid, 
                              container->host_uid, 1, &single_mapping_len, err);
    ret = write_file(uid_map_file, single_mapping, single_mapping_len, err);
}
```

### 3. 禁用setgroups
```c
static int deny_setgroups(libcrun_container_t *container, pid_t pid, libcrun_error_t *err) {
    xasprintf(&groups_file, "/proc/%d/setgroups", pid);
    ret = write_file(groups_file, "deny", 4, err);
}
```

## 典型映射场景

### 场景1：简单的1:1映射
- Host UID: 1000 → Container UID: 1000
- 映射格式: `1000 1000 1`

### 场景2：使用子ID范围的复杂映射
假设用户1000在/etc/subuid中有范围100000:65536：
```
0 100000 1000      # 容器中的0-999映射到host的100000-100999
1000 1000 1        # 容器中的1000映射到host的1000
1001 101000 64536  # 容器中的1001-65536映射到host的101000-165535
```

## 权限和安全考虑

### 1. 根用户检测
```c
static bool root_mapped_in_container_p(runtime_spec_schema_defs_id_mapping **mappings, size_t len) {
    for (i = 0; i < len; i++)
        if (mappings[i]->container_id == 0)
            return true;
    return false;
}
```

### 2. 容器内用户设置
```c
if (def->linux->uid_mappings_len != 0) {
    root_mapped = root_mapped_in_container_p(def->linux->uid_mappings, def->linux->uid_mappings_len);
    if (!root_mapped)
        uid = def->process->user->uid;  // 使用配置中指定的UID
}
```

## 实际映射示例

### 示例1：用户1000的简单映射
```bash
# /etc/subuid 内容
user1000:100000:65536

# 生成的映射
0 100000 1000      # 容器root(0-999) -> host(100000-100999)
1000 1000 1        # 容器用户1000 -> host用户1000
1001 101000 64536  # 容器其他用户 -> host子ID范围
```

### 示例2：写入proc文件系统的映射
```c
// 实际写入 /proc/[pid]/uid_map 的内容
"1000 1000 1\n"    // 简单1:1映射

// 或复杂映射
"0 100000 1000\n1000 1000 1\n1001 101000 64536\n"
```

## 关键函数调用链

```
libcrun_container_run()
├── make_container()                    // 设置host_uid/host_gid
├── libcrun_run_container()
    ├── is_rootless()                   // 检测rootless模式
    ├── configure_namespaces()          // 强制CLONE_NEWUSER
    └── libcrun_set_usernamespace()     // 设置用户命名空间映射
        ├── format_default_id_mapping() // 生成默认映射
        │   └── getsubidrange()         // 读取/etc/subuid
        ├── newuidmap()/newgidmap()     // 尝试使用映射工具
        └── write_file()                // 直接写入proc文件系统
```

## 错误处理和回退机制

### 1. newuidmap失败时的回退
```c
if (container->host_uid)
    ret = newuidmap(pid, uid_map, err);
if (container->host_uid == 0 || ret < 0) {
    // 回退到直接写入proc文件系统
    xasprintf(&uid_map_file, "/proc/%d/uid_map", pid);
    ret = write_file(uid_map_file, uid_map, uid_map_len, err);
}
```

### 2. 单一映射的特殊处理
```c
if (is_single_mapping(def->linux->uid_mappings, def->linux->uid_mappings_len,
                     container->host_uid, container->container_uid)) {
    // 使用简化的映射格式
    ret = format_mount_mapping(&single_mapping, container->container_uid,
                              container->host_uid, 1, &single_mapping_len, err);
}
```

## 安全性考虑

### 1. 权限检查
- 只有在rootless模式下才允许非特权用户创建用户命名空间
- 通过子ID范围限制用户可映射的ID范围
- 禁用setgroups防止权限提升

### 2. 映射验证
- 验证映射的合法性和范围
- 确保不会映射到系统保留的ID范围
- 检查用户是否有权限使用指定的子ID范围

## 总结

crun的rootless模式通过以下步骤实现用户ID映射：

1. **检测运行环境**：确定是否为rootless模式
2. **强制用户命名空间**：非root用户必须使用用户命名空间
3. **获取子ID范围**：从/etc/subuid和/etc/subgid读取可用范围
4. **生成映射规则**：创建host UID到容器UID的映射
5. **应用映射**：通过newuidmap/newgidmap或直接写入proc文件系统
6. **设置容器内身份**：在容器内设置最终的用户和组ID

这种机制确保了非特权用户可以安全地运行容器，同时保持适当的权限隔离。关键在于利用Linux用户命名空间的特性，将host上的非特权用户映射为容器内的相应用户，从而实现既安全又功能完整的rootless容器运行环境。
