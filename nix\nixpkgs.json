{"url": "https://github.com/nixos/nixpkgs", "rev": "66b41287a6d4088e07219991720702b1cc9a146b", "date": "2025-05-07T16:10:03+02:00", "path": "/nix/store/kl1l84aab70y4hzy1rn9sk7726q7yli3-nixpkgs", "sha256": "05i46f27847ym9h53nf6n441lqg6nzyr34wm9f7apspzllay43g1", "hash": "sha256-4Q3iFaX/6quOS5WTkf235mEaCLHG2VFgqv4QdIQzJBY=", "fetchLFS": false, "fetchSubmodules": false, "deepClone": false, "leaveDotGit": false}