name: Test

on: [push, pull_request]

jobs:
  build_job:
    runs-on: ubuntu-latest
    name: Build on ${{ matrix.arch }}
    permissions:
      contents: read
      packages: write

    strategy:
      matrix:
        include:
          - arch: armv7
            distro: ubuntu_latest
#          - arch: aarch64
#            distro: ubuntu_latest
#          - arch: s390x
#            distro: ubuntu_latest
#          - arch: ppc64le
#            distro: ubuntu_latest
          - arch: riscv64
            distro: ubuntu_latest
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true
          set-safe-directory: true

      - uses: uraimo/run-on-arch-action@v3.0.1
        name: Build
        id: build
        with:
          arch: ${{ matrix.arch }}
          distro: ${{ matrix.distro }}

          githubToken: ${{ github.token }}

          install: |
            apt-get update -y
            apt-get install -y automake libtool autotools-dev libseccomp-dev git make libcap-dev cmake pkg-config gcc wget go-md2man libsystemd-dev gperf clang-format libyajl-dev libprotobuf-c-dev clang mawk

          run: |
            find $(pwd) -name '.git' -exec bash -c 'git config --global --add safe.directory ${0%/.git}' {} \;
            ./autogen.sh
            ./configure CFLAGS='-Wall -Werror' || cat config.log
            make -j $(nproc) -C libocispec libocispec.la
            make git-version.h
            make -j $(nproc) libcrun.la
            make -j $(nproc) crun

            make -j $(nproc) clean

            if ./configure CFLAGS='-Wall -Werror --enable-shared'; then
                        make -j $(nproc) -C libocispec libocispec.la
                        make git-version.h
                        make -j $(nproc) libcrun.la
                        make -j $(nproc) crun
            fi

  Test:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        include:
          - test: disable-systemd
          - test: check
          - test: podman
          #- test: cri-o
          - test: containerd
          - test: oci-validation
          - test: alpine-build
          - test: centos8-build
          - test: centos9-build
          #- test: centos10-build
          - test: clang-format
          - test: clang-check
          - test: checkpoint-restore
          - test: fuzzing
          - test: codespell
          - test: wasmedge-build
    steps:
      - name: checkout
        uses: actions/checkout@v4

      - name: install dependencies
        run: |
          # If Dockerfile is present in test directory, the test is run
          # inside container, so these dependencies won't be needed.
          test -f "tests/${{ matrix.test }}/Dockerfile" && exit 0

          sudo add-apt-repository -y ppa:criu/ppa
          # add-apt-repository runs apt-get update so we don't have to.
          sudo apt-get install -q -y criu automake libtool autotools-dev libseccomp-dev git make libcap-dev cmake pkg-config gcc wget go-md2man libsystemd-dev gperf clang-format libyajl-dev containerd runc libasan6 libprotobuf-c-dev mawk

      - name: run autogen.sh
        run: |
          git clean -fdx .
          find $(pwd) -name '.git' -exec bash -c 'git config --global --add safe.directory ${0%/.git}' {} \;
          ./autogen.sh

      - name: run test
        run: |
          case "${{ matrix.test }}" in
              disable-systemd)
                  ./configure --disable-systemd
                  make -j $(nproc)
              ;;
              check)
                  sudo sysctl -w kernel.apparmor_restrict_unprivileged_userns=0
                  ./configure --disable-dl
                  make
                  make syntax-check
                  echo run tests as root
                  sudo make check ASAN_OPTIONS=detect_leaks=false || (cat test-suite.log; exit 1)
                  echo run tests as rootless
                  make check ASAN_OPTIONS=detect_leaks=false || (cat test-suite.log; exit 1)
                  echo run tests as rootless in a user namespace
                  unshare -r make check ASAN_OPTIONS=detect_leaks=false || (cat test-suite.log; exit 1)

                  git status
                  git diff

                  # check that the working dir is clean
                  git describe --broken --dirty --all | grep -qv dirty
              ;;
              podman)
                  sudo mkdir -p /var/lib/containers /var/tmp
                  sudo docker build -t crun-podman tests/podman
                  sudo docker run --cgroupns=host --privileged --rm -v /var/tmp:/var/tmp:rw -v /var/lib/containers:/var/lib/containers:rw -v /sys/fs/cgroup:/sys/fs/cgroup:rw,rslave -v ${PWD}:/crun crun-podman
                  ;;
              #cri-o)
              #    sudo mkdir -p /var/lib/var-crio/tmp /var/lib/tmp-crio /var/lib/var-tmp-crio
              #    sudo docker build -t crun-cri-o tests/cri-o
              #    sudo docker run --cgroupns=host  --net host --privileged --rm -v /dev/zero:/sys/module/apparmor/parameters/enabled -v /var/lib/tmp-crio:/tmp:rw -v /var/lib/var-tmp-crio:/var/tmp -v /var/lib/var-crio:/var/lib/containers:rw -v /sys/fs/cgroup:/sys/fs/cgroup:rw,rslave -v ${PWD}:/crun crun-cri-o
              #    ;;
              containerd)
                  sudo mkdir -p /var/lib/var-containerd
                  sudo docker build -t crun-containerd tests/containerd
                  sudo docker run --cgroupns=host --privileged --net host --rm -v /tmp:/tmp:rw -v /var/lib/var-containerd:/var/lib:rw -v /sys:/sys:rw,rslave -v ${PWD}:/crun crun-containerd
                  ;;
              oci-validation)
                  sudo docker build -t crun-oci-validation tests/oci-validation
                  sudo docker run --cgroupns=host --privileged --rm -v /sys/fs/cgroup:/sys/fs/cgroup:rw,rslave -v ${PWD}:/crun crun-oci-validation
              ;;
              alpine-build)
                  sudo docker build -t crun-alpine-build tests/alpine-build
                  sudo docker run --cgroupns=host --privileged --rm -v /sys/fs/cgroup:/sys/fs/cgroup:rw,rslave -v ${PWD}:/crun crun-alpine-build
              ;;
              centos8-build)
                  sudo docker build -t crun-centos8-build tests/centos8-build
                  sudo docker run --cgroupns=host --privileged --rm -v /sys/fs/cgroup:/sys/fs/cgroup:rw,rslave -v ${PWD}:/crun crun-centos8-build
              ;;
              centos9-build)
                  sudo docker build -t crun-centos9-build tests/centos9-build
                  sudo docker run --cgroupns=host --privileged --rm -v /sys/fs/cgroup:/sys/fs/cgroup:rw,rslave -v ${PWD}:/crun crun-centos9-build
              ;;
              centos10-build)
                  sudo docker build -t crun-centos10-build tests/centos10-build
                  sudo docker run --cgroupns=host --privileged --rm -v /var/tmp:/var/tmp:rw -v /var/lib/containers:/var/lib/containers:rw -v /sys/fs/cgroup:/sys/fs/cgroup:rw,rslave -v ${PWD}:/crun crun-centos10-build
              ;;
              clang-format)
                  sudo docker build -t crun-clang-format tests/clang-format
                  sudo docker run --rm -w /crun -v ${PWD}:/crun crun-clang-format
              ;;
              clang-check)
                  sudo docker build -t crun-clang-check tests/clang-check
                  sudo docker run --privileged --rm -w /crun -v ${PWD}:/crun crun-clang-check
              ;;
              checkpoint-restore)
                  ./configure
                  make -j $(nproc)
                  sudo python3 tests/test_checkpoint_restore.py
              ;;
              fuzzing)
                  sudo docker build -t crun-fuzzing tests/fuzzing
                  sudo docker run --cgroupns=host -e RUN_TIME=300 --privileged --rm -v /sys/fs/cgroup:/sys/fs/cgroup:rw,rslave -v ${PWD}:/crun crun-fuzzing
              ;;
              codespell)
                  sudo docker build -t crun-codespell tests/codespell
                  sudo docker run --rm -w /crun -v ${PWD}:/crun crun-codespell codespell -q 0
              ;;
              wasmedge-build)
                  sudo docker build -t wasmedge tests/wasmedge-build
                  sudo docker run --privileged --cgroupns=host --rm -v containers:/var/lib/containers:rw -v /sys/fs/cgroup:/sys/fs/cgroup:rw,rslave  -w /crun -v ${PWD}:/crun wasmedge
              ;;
          esac

  shellcheck:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: install shellcheck
        env:
          VERSION: v0.10.0
          BASEURL: https://github.com/koalaman/shellcheck/releases/download
          SHA256: f35ae15a4677945428bdfe61ccc297490d89dd1e544cc06317102637638c6deb
        run: |
          mkdir ~/bin
          curl -sSfL --retry 5 $BASEURL/$VERSION/shellcheck-$VERSION.linux.x86_64.tar.xz |
            tar xfJ - -C ~/bin --strip 1 shellcheck-$VERSION/shellcheck
          sha256sum --strict --check - <<<"$SHA256 *$HOME/bin/shellcheck"
          # make sure to remove the old version
          sudo rm -f /usr/bin/shellcheck
          # Add ~/bin to $PATH.
          echo ~/bin >> $GITHUB_PATH
      - name: install dependencies
        run: |
          sudo apt-get update -q -y
          sudo apt-get install -q -y automake libtool autotools-dev libseccomp-dev git make libcap-dev cmake pkg-config gcc wget go-md2man libsystemd-dev gperf clang-format libyajl-dev libprotobuf-c-dev mawk
      - uses: lumaxis/shellcheck-problem-matchers@v2
      - name: shellcheck
        run: |
          find $(pwd) -name '.git' -exec bash -c 'git config --global --add safe.directory ${0%/.git}' {} \;
          ./autogen.sh
          ./configure
          make shellcheck
